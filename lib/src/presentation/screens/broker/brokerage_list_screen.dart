import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/utils/column_mapping_utils.dart';
import '/src/domain/models/filter/table_filter.dart';
import '../../cubit/filter/filter_cubit.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '../../../core/enum/user_status.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../domain/models/broker_api.dart';
import '../../../domain/models/user.dart';
import '../../cubit/broker/broker_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/tables/action_button_eye.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';

class BrokerageListScreen extends HookWidget {
  final Function(Brokers)? onNavigateToAgentNetwork;
  BrokerageListScreen({super.key, required this.onNavigateToAgentNetwork});

  /// Gets the backend field name for a given display column name
  /// Uses the shared ColumnMappingUtils for consistency across the app
  static String _getBackendColumnName(String displayColumnName) {
    return ColumnMappingUtils.getBrokerBackendColumnName(displayColumnName);
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final sortedBrokers = useState<List<Brokers>>([]);
    final brokerFilterOptions = useState<List<TableFilter>>([]);
    final userStatusFilterOptions = useState<List<TableFilter>>([]);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalBrokers = useState(0);
    final ValueNotifier<String?> searchString = useState('');
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final ValueNotifier<bool?> isActive = useState(null);
    final ValueNotifier<String?> associatedBrokerage = useState(null);
    final ValueNotifier<String?> sortBy = useState("created_at");
    final ValueNotifier<String?> sortOrder = useState("ASC");
    final currentFilters = useState<Map<String, dynamic>>({});

    final user = context.watch<UserCubit>().state.user;
    final filterCubit = context.read<FilterCubit>();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListStateColumnHeader,
      brokerListCityColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
      brokerListTotalRevenueColumnHeader,
      brokerListCommissionColumnHeader,
      brokerListStatusColumnHeader,
    ];

    void handleSort(String columnName, bool ascending) async {
      // Map the display column name to the backend field name
      String backendColumnName = _getBackendColumnName(columnName);
      sortBy.value = backendColumnName;
      sortOrder.value = ascending ? 'ASC' : 'DESC';

      if (context.mounted) {
        await _fetchBrokers(
          context,
          user,
          selectedDate: selectedDate.value,
          page: currentpage.value,
          searchString: searchString.value,
          isActive: isActive.value,
          associatedBrokerage: associatedBrokerage.value,
          sortColumn: backendColumnName,
          sortOrder: sortOrder.value,
        );
      }
    }

    useEffect(() {
      Future.microtask(() async {
        if (context.mounted) {
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
            isActive: isActive.value,
            associatedBrokerage: associatedBrokerage.value,
            sortColumn: sortBy.value,
            sortOrder: sortOrder.value,
          );
          await filterCubit.getBrokerageFilterOptions();
          final state = filterCubit.state;
          if (state is FilterLoaded) {
            brokerFilterOptions.value = state.filterOptions;
          }

          await filterCubit.getUserStatusFilterOptions();
          final statusState = filterCubit.state;
          if (statusState is FilterLoaded) {
            userStatusFilterOptions.value = statusState.filterOptions;
          }
        }
      });
      return null;
    }, []);

    return BlocConsumer<BrokerCubit, BrokerState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        if (state is BrokerLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.brokerApi?.totalPages ?? 0;
            sortedBrokers.value = state.brokerApi?.brokers ?? [];
            totalBrokers.value = state.brokerApi?.totalElements ?? 0;
          });
        }

        return CustomDataTableWidget<Brokers>(
          data: sortedBrokers.value,
          title: brokersTab,
          titleIcon: "$iconAssetpath/user.png",
          searchHint: searchHint,
          searchFn: (broker) =>
              broker.fullName +
              broker.phone +
              broker.email +
              broker.city +
              broker.state +
              AppDateFormatter.formatJoiningDate(broker.joiningDate) +
              broker.totalDownlineAgents.toString() +
              broker.totalSales.toString() +
              broker.totalRevenue.toString(),
          // Dynamic filtering system
          filterColumnNames: [
            brokerListNameColumnHeader,
            brokerListJoinDateColumnHeader,
            brokerListStatusColumnHeader,
          ],
          filterValueExtractors: {
            brokerListJoinDateColumnHeader: (broker) {
              return AppDateFormatter.formatJoiningDate(broker.joiningDate);
            },
          },
          // Date filter configuration - specify which columns should use date filters
          dateFilterColumns: const [
            brokerListJoinDateColumnHeader, // Join date should use calendar picker
          ],
          filterOptions: {
            if (brokerFilterOptions != null)
              brokerListNameColumnHeader: brokerFilterOptions.value,
            if (userStatusFilterOptions.value.isNotEmpty)
              brokerListStatusColumnHeader: userStatusFilterOptions.value,
          },
          columnNames: formattedHeaders,
          cellBuilders: [
            (broker) => broker.fullName,
            (broker) => broker.phone,
            (broker) => broker.email,
            (broker) => AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
            (broker) => broker.state,
            (broker) => broker.city,
            (broker) => broker.totalDownlineAgents.toString(),
            (broker) => '${broker.totalSales}',
            (broker) => '\$${broker.totalRevenue}',
            (broker) => '\$${broker.commission}',
            (broker) => broker.isActive
                ? UserStatus.active.value
                : UserStatus.inactive.value,
          ],
          iconCellBuilders: [
            (broker) => TableCellData(
              text: broker.fullName,
              leftIconAsset: "$iconAssetpath/agent_round.png",
              iconSize: 30,
            ),
            null, // contact - no icon
            null, // email - no icon
            null, // address - no icon
            null, // joinDate - no icon
            null, // agents - no icon
            null, // totalSales - no icon
            null, // totalrevenue - no icon
            null, // commision- no icon
            null, // city - no icon
            null, // status - no icon
          ],
          useIconBuilders: [
            true, // name - use icon
            false, // contact - use text
            false, // email - use text
            false, // address - use text
            false, // joinDate - use text
            false, // agents - use text
            false, // totalSales - use text
            false, // totalrevenue - use text
            false, // commision - use text
            false, // city - use text
            false, // status - use text
          ],
          widgetCellBuilders: [
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            (context, broker) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: broker.isActive
                    ? AppTheme.agentStatusActiveBg.withAlpha(36)
                    : AppTheme.agentStatusInactiveBg.withAlpha(36),
                borderRadius: BorderRadius.circular(
                  20,
                ), // More rounded for oval shape
              ),
              child: Text(
                broker.isActive
                    ? UserStatus.active.value
                    : UserStatus.inactive.value,
                style: AppFonts.mediumTextStyle(
                  12,
                  color: broker.isActive
                      ? AppTheme
                            .statusActiveText // Darker green text
                      : AppTheme.statusInactiveText,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          useWidgetBuilders: [
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            false,
            true, // status
          ],
          actionBuilders: [
            (context, broker) => ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isCompact: true,
              isMobile: false,
            ),
          ],

          mobileCardBuilder: (context, broker) =>
              _buildMobileBrokerCard(broker, context),
          onSort: handleSort,
          emptyStateMessage: noDataAvailable,
          pageCount: pageCount.value,
          totalElements: totalBrokers.value,
          isLoading: state is BrokerLoading,
          useExpandedView: false,
          // onDateFilterChanged: (value) async {
          //   selectedDate.value = value;
          //   await _fetchBrokers(
          //     context,
          //     user,
          //     selectedDate: value,
          //     page: currentpage.value,
          //     searchString: searchString.value,
          //   );
          // },
          onAllFiltersChanged: (allFilters) async {
            currentFilters.value = allFilters;
            DateTime? joiningDate;
            String? statusId;
            String? brokerageId;
            //brokerListNameColumnHeader
            if (allFilters.containsKey(brokerListJoinDateColumnHeader)) {
              joiningDate =
                  allFilters[brokerListJoinDateColumnHeader] as DateTime?;
              selectedDate.value = joiningDate;
            } else {
              selectedDate.value = null;
            }

            if (allFilters.containsKey(brokerListStatusColumnHeader)) {
              statusId = allFilters[brokerListStatusColumnHeader] as String?;
              final selectedFilter = userStatusFilterOptions.value.firstWhere(
                (filter) => filter.id == statusId,
                orElse: () => TableFilter(
                  id: '',
                  key: '',
                  value: '',
                ), // Default empty filter
              );
              if (selectedFilter.value == UserStatus.active.value) {
                isActive.value = true;
              } else if (selectedFilter.value == UserStatus.inactive.value) {
                isActive.value = false;
              } else if (selectedFilter.value ==
                  UserStatus.pendingVerification.value) {
                isActive.value = null;
              } else {
                isActive.value = null;
              }
            } else {
              isActive.value = null;
            }

            if (allFilters.containsKey(brokerListNameColumnHeader)) {
              brokerageId = allFilters[brokerListNameColumnHeader] as String?;
              associatedBrokerage.value = brokerageId;
            } else {
              associatedBrokerage.value = null;
            }

            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              page: currentpage.value,
              searchString: searchString.value,
              isActive: isActive.value,
              associatedBrokerage: associatedBrokerage.value,
              sortColumn: sortBy.value,
              sortOrder: sortOrder.value,
            );
          },
          handleTableSearch: (value) async {
            searchString.value = value;
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              page: currentpage.value,
              searchString: value,
              isActive: isActive.value,
              associatedBrokerage: associatedBrokerage.value,
              sortColumn: sortBy.value,
              sortOrder: sortOrder.value,
            );
          },
          handlePagination: (page) async {
            currentpage.value = page;
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              searchString: searchString.value,
              page: page,
              isActive: isActive.value,
              associatedBrokerage: associatedBrokerage.value,
              sortColumn: sortBy.value,
              sortOrder: sortOrder.value,
            );
          },
        );
      },
    );
  }

  void _onBrokerAction(BuildContext context, Brokers broker) {
    if (onNavigateToAgentNetwork != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetwork!(broker);
      } catch (e) {
        // If no matching broker found, show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Broker not found: ${broker.fullName}')),
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Action clicked for ${broker.fullName}')),
      );
    }
  }

  Widget _buildMobileBrokerCard(Brokers broker, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                broker.fullName,
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$brokerListContactColumnHeader: ${broker.phone}'),
          Text('$brokerListEmailColumnHeader: ${broker.email}'),
          Text('$brokerListCityColumnHeader: ${broker.city}'),
          Text('$brokerListStateColumnHeader: ${broker.state}'),
          Text(
            '$brokerListJoinDateColumnHeader: ${AppDateFormatter.formatJoiningDate(broker.joiningDate)}',
          ),

          Text('$brokerListAgentsColumnHeader: ${broker.totalDownlineAgents}'),
          Text('$brokerListTotalSalesColumnHeader: ${broker.totalSales}'),
          Text(
            '$brokerListTotalRevenueColumnHeader: \$${broker.totalRevenue.toStringAsFixed(2)}',
          ),
          Text(
            '$brokerListCommissionColumnHeader: \$${broker.commission.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _fetchBrokers(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required int page,
    required String? searchString,
    required bool? isActive,
    required String? associatedBrokerage,
    required String? sortColumn,
    required String? sortOrder,
  }) async {
    String? formattedDate = selectedDate != null
        ? AppDateFormatter.formatDateyyyyMMdd(selectedDate)
        : null;

    // TODO: update sortBy static value when server side updates
    final payload = {
      "page": page > 0 ? page - 1 : 0,
      "size": 10,
      "sortBy": sortColumn ?? "created_at",
      "sortDirection": sortOrder ?? "ASC",
      "searchString": searchString,
      "associatedBrokerageId": associatedBrokerage,
      "agentId": null,
      "active": isActive,
      "joiningDate": formattedDate,
      "userId": user?.userId,
    };
    await context.read<BrokerCubit>().fetchBrokers(payload);
  }
}
